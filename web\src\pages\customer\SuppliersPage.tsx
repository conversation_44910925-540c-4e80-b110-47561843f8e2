import React, { useState, useMemo, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Search, Star, Clock, Phone, MapPin, ArrowLeft, X, Grid, List, Zap, Award, TrendingUp, Heart, Share2, AlertCircle, RefreshCw } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { apiService, comprehensiveSearch } from '../../services/api';
import type { Supplier, ComprehensiveSearchResults } from '../../services/api';

// Helper function to check if supplier is currently open
const isSupplierOpen = (openHours: string): boolean => {
  try {
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes(); // Current time in minutes

    // Parse openHours format like "10:00 AM - 11:00 PM"
    const [startTime, endTime] = openHours.split(' - ');

    const parseTime = (timeStr: string): number => {
      const [time, period] = timeStr.trim().split(' ');
      const [hours, minutes] = time.split(':').map(Number);
      let totalMinutes = hours * 60 + minutes;

      if (period === 'PM' && hours !== 12) {
        totalMinutes += 12 * 60;
      } else if (period === 'AM' && hours === 12) {
        totalMinutes = minutes;
      }

      return totalMinutes;
    };

    const startMinutes = parseTime(startTime);
    const endMinutes = parseTime(endTime);

    // Handle cases where end time is next day (like 11:00 PM to 2:00 AM)
    if (endMinutes < startMinutes) {
      return currentTime >= startMinutes || currentTime <= endMinutes;
    }

    return currentTime >= startMinutes && currentTime <= endMinutes;
  } catch (error) {
    // If parsing fails, assume open
    return true;
  }
};

const SuppliersPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const category = searchParams.get('category') || 'all';
  const [search, setSearch] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'rating' | 'distance' | 'delivery' | 'popular'>('popular');
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);

  // Backend integration state
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchResults, setSearchResults] = useState<ComprehensiveSearchResults | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [showSearchResults, setShowSearchResults] = useState(false);

  // Load suppliers from backend API
  useEffect(() => {
    const loadSuppliers = async () => {
      setLoading(true);
      setError(null);
      try {
        const params: any = {};

        // Add category filter if not 'all'
        if (category !== 'all') {
          params.category = category;
        }

        // Add sorting parameters
        params.sortBy = sortBy === 'popular' ? 'rating' : sortBy;
        params.sortOrder = 'desc';
        params.limit = 50; // Load more suppliers for better UX

        const response = await apiService.getSuppliers(params);
        if (response.success && response.data) {
          // Filter only active suppliers
          const activeSuppliers = response.data.filter(supplier => supplier.isActive);
          setSuppliers(activeSuppliers);
        } else {
          throw new Error(response.message || 'Failed to load suppliers');
        }
      } catch (error) {
        console.error('Error loading suppliers:', error);
        setError('Failed to load suppliers. Please try again.');
        setSuppliers([]);
      } finally {
        setLoading(false);
      }
    };

    loadSuppliers();
  }, [category, sortBy]);

  // Handle scroll for header animation with debouncing (matching home page pattern)
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // Clear previous timeout
      clearTimeout(timeoutId);

      // Debounce the header compact state change
      timeoutId = setTimeout(() => {
        // Much higher threshold to prevent premature hiding
        setIsHeaderCompact(currentScrollY > 800);
      }, 100);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, []);

  // Search functionality using the same API as web customer home page
  useEffect(() => {
    const performSearch = async () => {
      if (!search.trim()) {
        setSearchResults(null);
        setShowSearchResults(false);
        return;
      }

      setIsSearching(true);
      try {
        // Use real API search but filter out services and categories as requested
        const results = await comprehensiveSearch(search);

        // Filter out services and categories as requested
        const filteredResults: ComprehensiveSearchResults = {
          services: [], // Ignore services results
          categories: [], // Ignore categories results
          suppliers: results.suppliers,
          products: results.products,
          total: results.suppliers.length + results.products.length
        };

        setSearchResults(filteredResults);
        setShowSearchResults(true);
      } catch (error) {
        console.error('Error performing search:', error);
        setSearchResults(null);
        setShowSearchResults(false);
      } finally {
        setIsSearching(false);
      }
    };

    const timeoutId = setTimeout(performSearch, 300); // Debounce search
    return () => clearTimeout(timeoutId);
  }, [search]);

  // Dynamic filtering and sorting - 100% backend compatible
  const filteredSuppliers = useMemo(() => {
    let suppliersToFilter = suppliers;

    // If search is active and we have search results, use suppliers from search
    if (search.trim() && searchResults) {
      // Extract supplier data from search results
      const searchSupplierIds = searchResults.suppliers.map(result => result.id);
      suppliersToFilter = suppliers.filter(supplier =>
        searchSupplierIds.includes(supplier.id) || searchSupplierIds.includes(supplier._id)
      );

      // Also include suppliers that match the search term directly
      const directMatches = suppliers.filter(supplier =>
        supplier.name.toLowerCase().includes(search.toLowerCase()) ||
        supplier.tags?.some(tag => tag.toLowerCase().includes(search.toLowerCase())) ||
        supplier.description?.toLowerCase().includes(search.toLowerCase())
      );

      // Combine and deduplicate
      const combinedIds = new Set([
        ...suppliersToFilter.map(s => s.id),
        ...directMatches.map(s => s.id)
      ]);
      suppliersToFilter = suppliers.filter(supplier => combinedIds.has(supplier.id));
    }

    // Apply additional local search filtering for better UX
    if (search.trim() && !showSearchResults) {
      suppliersToFilter = suppliersToFilter.filter(supplier =>
        supplier.name.toLowerCase().includes(search.toLowerCase()) ||
        supplier.tags?.some(tag => tag.toLowerCase().includes(search.toLowerCase())) ||
        supplier.description?.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Sort suppliers (backend already provides initial sorting, but we can refine)
    suppliersToFilter.sort((a, b) => {
      switch (sortBy) {
        case 'rating':
          return b.rating - a.rating;
        case 'distance':
          // For now, use random sorting as distance calculation requires user location
          return Math.random() - 0.5;
        case 'delivery':
          // Parse delivery time for sorting (e.g., "30-45 mins" -> 30)
          const getDeliveryMinutes = (time: string) => {
            const match = time.match(/(\d+)/);
            return match ? parseInt(match[1]) : 999;
          };
          return getDeliveryMinutes(a.deliveryTime) - getDeliveryMinutes(b.deliveryTime);
        case 'popular':
        default:
          return b.rating - a.rating; // Default to rating for popularity
      }
    });

    return suppliersToFilter;
  }, [suppliers, search, sortBy, searchResults, showSearchResults]);

  // Dynamic promotions from suppliers with discounted products - 100% backend compatible
  const dynamicPromotions = useMemo(() => {
    const promotions: Array<{ id: string; title: string; category: string; supplier?: Supplier; product?: any }> = [];

    // Find the product with the largest discount from each supplier
    filteredSuppliers.forEach(supplier => {
      if (supplier.products && supplier.products.length > 0) {
        const discountedProducts = supplier.products.filter(product =>
          product.discountPrice && product.discountPrice > 0 && product.discountPrice < product.price
        );

        if (discountedProducts.length > 0) {
          function discountPercentage(actualPrice: number, discountPrice?: number) {
            return Math.round(
              ((actualPrice - (discountPrice || 0)) / actualPrice) * 100
            );
          }

          // Find the product with the largest discount amount (not percentage)
          let maxDiscount = 0;
          const productWithLargestDiscount = discountedProducts.reduce((maxProduct, currentProduct) => {
            const currentDiscount = discountPercentage(currentProduct.price, currentProduct.discountPrice);
            if (currentDiscount > maxDiscount) {
              
            }
            return currentDiscount > maxDiscount ? currentProduct : maxProduct;
          });

          promotions.push({
            id: `promo-${supplier.id}`,
            title: `${discountPercentage}% Off ${productWithLargestDiscount.name}`,
            category: supplier.category,
            supplier,
            product: productWithLargestDiscount
          });
        }
      }
    });

    return promotions.slice(0, 10);
  }, [filteredSuppliers]);

  // Retry function for error handling
  const retryLoading = () => {
    setError(null);
    setLoading(true);
    // Re-trigger the useEffect by updating a dependency
    const loadSuppliers = async () => {
      try {
        const params: any = {};

        if (category !== 'all') {
          params.category = category;
        }

        params.sortBy = sortBy === 'popular' ? 'rating' : sortBy;
        params.sortOrder = 'desc';
        params.limit = 50;

        const response = await apiService.getSuppliers(params);
        if (response.success && response.data) {
          const activeSuppliers = response.data.filter(supplier => supplier.isActive);
          setSuppliers(activeSuppliers);
        } else {
          throw new Error(response.message || 'Failed to load suppliers');
        }
      } catch (error) {
        console.error('Error loading suppliers:', error);
        setError('Failed to load suppliers. Please try again.');
        setSuppliers([]);
      } finally {
        setLoading(false);
      }
    };

    loadSuppliers();
  };

  const getCategoryLabel = (cat: string) => {
    // Handle special case for 'all'
    if (cat === 'all') return 'All Suppliers';

    // Convert to title case: capitalize first letter of each word
    return cat
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  const handleSupplierClick = (supplierId: string) => {
    navigate(`/customer/supplier-details?supplierId=${supplierId}`);
  };

  const toggleFavorite = (supplierId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(supplierId)) {
        newFavorites.delete(supplierId);
      } else {
        newFavorites.add(supplierId);
      }
      return newFavorites;
    });
  };

  const clearSearch = () => {
    setSearch('');
  };

  const handleBackPress = () => {
    navigate(-1);
  };

  return (
    <>
      {/* Sticky Header with Scroll Animation - Matching Home Page Pattern */}
      <motion.div
        className="fixed top-0 left-0 right-0 z-40 transition-all duration-500"
        animate={{
          backgroundColor: isHeaderCompact
            ? "rgba(15, 23, 42, 0.95)"
            : "rgba(15, 23, 42, 0)",
          backdropFilter: isHeaderCompact ? "blur(20px)" : "blur(0px)",
          borderBottom: isHeaderCompact
            ? "1px solid rgba(255, 255, 255, 0.1)"
            : "1px solid rgba(255, 255, 255, 0)",
          pointerEvents: isHeaderCompact ? "auto" : "none",
        }}
        transition={{ duration: 0.3 }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            animate={{
              paddingTop: isHeaderCompact ? "1rem" : "2rem",
              paddingBottom: isHeaderCompact ? "1rem" : "2rem",
            }}
            transition={{ duration: 0.3 }}
          >
            {/* Compact Header Content */}
            <motion.div
              animate={{
                opacity: isHeaderCompact ? 1 : 0,
                height: isHeaderCompact ? "auto" : 0,
              }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleBackPress}
                    className="p-2 bg-white/10 hover:bg-white/20 rounded-xl transition-all duration-200"
                  >
                    <ArrowLeft className="text-white" size={20} />
                  </motion.button>
                  <div>
                    <h1 className="text-xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent">
                      {getCategoryLabel(category)}
                    </h1>
                    <p className="text-white/60 text-xs">Choose your supplier</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  {/* Mini Search */}
                  <div className="hidden md:flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 px-3 py-2">
                    <Search size={16} className="text-white/60" />
                    <input
                      type="text"
                      placeholder="Search..."
                      value={search}
                      onChange={(e) => setSearch(e.target.value)}
                      className="bg-transparent text-white placeholder-white/50 outline-none text-sm w-32"
                    />
                  </div>

                  {/* View Mode Toggle */}
                  <div className="flex items-center bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 p-1">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`p-2 rounded-md transition-all duration-200 ${
                        viewMode === 'grid'
                          ? 'bg-white/20 text-white'
                          : 'text-white/60 hover:text-white'
                      }`}
                    >
                      <Grid size={16} />
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`p-2 rounded-md transition-all duration-200 ${
                        viewMode === 'list'
                          ? 'bg-white/20 text-white'
                          : 'text-white/60 hover:text-white'
                      }`}
                    >
                      <List size={16} />
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </motion.div>

      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
                  {/* Animated gradient orbs */}
                  <motion.div
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.3, 0.6, 0.3],
                    }}
                    transition={{
                      duration: 8,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-orange-500/30 to-red-600/30 rounded-full blur-3xl"
                  />
                  <motion.div
                    animate={{
                      scale: [1.2, 1, 1.2],
                      opacity: [0.4, 0.7, 0.4],
                    }}
                    transition={{
                      duration: 10,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 2
                    }}
                    className="absolute top-1/2 right-0 w-80 h-80 bg-gradient-to-br from-pink-500/30 to-purple-600/30 rounded-full blur-3xl"
                  />
                  <motion.div
                    animate={{
                      scale: [1, 1.3, 1],
                      opacity: [0.2, 0.5, 0.2],
                    }}
                    transition={{
                      duration: 12,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 4
                    }}
                    className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-blue-500/30 to-cyan-600/30 rounded-full blur-3xl"
                  />
        
                  {/* Floating particles */}
                  {[...Array(15)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-2 h-2 bg-white/20 rounded-full"
                      style={{
                        left: `${Math.random() * 100}%`,
                        top: `${Math.random() * 100}%`,
                      }}
                      animate={{
                        y: [-20, -100, -20],
                        opacity: [0, 1, 0],
                      }}
                      transition={{
                        duration: 3 + Math.random() * 2,
                        repeat: Infinity,
                        delay: Math.random() * 2,
                      }}
                    />
                  ))}
        </div>

        {/* Header Content - Normal Flow */}
        <div className="relative z-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8 pb-4">
              {/* Header Content */}
              <div className="text-center space-y-6 py-8">
                <div className="text-center space-y-6 py-8">
                  {/* Hero Section */}
                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.2 }}
                    className="space-y-4"
                  >
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={handleBackPress}
                      className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-xl transition-all duration-200 text-white/80 hover:text-white"
                    >
                      <ArrowLeft size={18} />
                      <span className="text-sm font-medium">Back</span>
                    </motion.button>

                    <div className="space-y-3">
                      <motion.h1
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: 0.4 }}
                        className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent"
                      >
                        {getCategoryLabel(category)}
                      </motion.h1>

                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: 0.6 }}
                        className="flex items-center justify-center gap-2 text-white/70"
                      >
                        <MapPin size={16} />
                        <span className="text-base">Nablus, Palestine</span>
                      </motion.div>

                      <motion.p
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: 0.8 }}
                        className="text-lg text-white/60 max-w-xl mx-auto"
                      >
                        Discover amazing suppliers and get your orders delivered fast
                      </motion.p>
                    </div>
                  </motion.div>

                  {/* Enhanced Search Section */}
                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 1.0 }}
                    className="max-w-xl mx-auto space-y-4"
                  >
                    <div className="bg-white/10 backdrop-blur-xl rounded-xl border border-white/20 p-4 shadow-2xl">
                      <div className="flex items-center gap-3">
                        <Search size={20} className="text-white/60" />
                        <input
                          type="text"
                          placeholder="Search suppliers..."
                          value={search}
                          onChange={(e) => setSearch(e.target.value)}
                          className="flex-1 bg-transparent text-white placeholder-white/50 outline-none"
                        />
                        {search && (
                          <motion.button
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={clearSearch}
                            className="p-1 hover:bg-white/20 rounded-lg transition-all duration-200"
                          >
                            <X size={16} className="text-white/60" />
                          </motion.button>
                        )}
                      </div>
                    </div>

                    {/* Filter and Sort Controls */}
                    <div className="flex items-center justify-center gap-3 flex-wrap">
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        className="bg-white/10 backdrop-blur-xl rounded-lg border border-white/20 px-3 py-2"
                      >
                        <select
                          value={sortBy}
                          onChange={(e) => setSortBy(e.target.value as any)}
                          className="bg-transparent text-white outline-none cursor-pointer text-sm"
                        >
                          <option value="popular" className="bg-slate-800 text-white">Most Popular</option>
                          <option value="rating" className="bg-slate-800 text-white">Highest Rated</option>
                          <option value="delivery" className="bg-slate-800 text-white">Fastest Delivery</option>
                          <option value="distance" className="bg-slate-800 text-white">Nearest</option>
                        </select>
                      </motion.div>

                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                        className="flex items-center gap-2 px-3 py-2 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-200 text-white text-sm"
                      >
                        {viewMode === 'grid' ? <List size={16} /> : <Grid size={16} />}
                        <span className="font-medium">
                          {viewMode === 'grid' ? 'List' : 'Grid'}
                        </span>
                      </motion.button>
                    </div>
                  </motion.div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="relative z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">

            {/* Loading State */}
            {loading && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center py-16"
              >
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="inline-block w-12 h-12 border-4 border-white/20 border-t-white rounded-full mb-4"
                />
                <h3 className="text-xl font-semibold text-white mb-2">Loading Suppliers...</h3>
                <p className="text-white/60">Finding the best suppliers for you</p>
              </motion.div>
            )}

            {/* Error State */}
            {error && !loading && (
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center py-16"
              >
                <motion.div
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="text-6xl mb-6"
                >
                  <AlertCircle className="w-16 h-16 text-red-400 mx-auto" />
                </motion.div>
                <h3 className="text-2xl font-bold text-white mb-4">Oops! Something went wrong</h3>
                <p className="text-white/60 text-lg mb-8 max-w-md mx-auto">{error}</p>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={retryLoading}
                  className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-medium hover:shadow-lg transition-all duration-200"
                >
                  <RefreshCw size={18} />
                  Try Again
                </motion.button>
              </motion.div>
            )}

            {/* Content - Only show when not loading and no error */}
            {!loading && !error && (
              <>
                {/* Promotions Section */}
                {dynamicPromotions.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="space-y-6"
              >
                <div className="flex items-center gap-3">
                  <motion.div
                    animate={{ rotate: [0, 10, -10, 0] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <Zap className="text-orange-400" size={28} />
                  </motion.div>
                  <h2 className="text-2xl font-bold text-white">Hot Promotions</h2>
                </div>

                {/* Enhanced horizontal scroll container with indicators */}
                <div className="relative">
                  {/* Horizontal scroll container */}
                  <div className="flex gap-6 overflow-x-auto pb-4 px-2 pt-2 scroll-smooth"
                       style={{
                         scrollbarWidth: 'thin',
                         scrollbarColor: 'rgba(255,255,255,0.3) transparent'
                       }}>
                    {dynamicPromotions.map((promotion, index) => (
                      <motion.div
                        key={promotion.id}
                        initial={{ opacity: 0, x: 50 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: index * 0.1 }}
                        whileHover={{
                          scale: 1.05,
                          y: -5
                        }}
                        className={
                          "mt-4 flex-shrink-0 min-w-[280px] max-w-[320px] bg-gradient-to-br from-orange-500 via-pink-500 to-purple-600 p-6 rounded-2xl border border-white/30 backdrop-blur-sm cursor-pointer" +
                          (index === 0 ? " ml-10" : "")
                        }
                      >
                        <div className="flex items-center justify-between mb-3">
                          <Award className="text-white/80" size={24} />
                          <span className="px-3 py-1 bg-white/20 rounded-full text-white text-xs font-medium">
                            Limited Time
                          </span>
                        </div>
                        <h3 className="text-xl font-bold text-white mb-2">{promotion.title}</h3>
                        <p className="text-white/80 text-sm">Available for {promotion.category}</p>

                        {/* Scroll hint for first item */}
                        {index === 0 && dynamicPromotions.length > 1 && (
                          <motion.div
                            animate={{ x: [0, 10, 0] }}
                            transition={{ duration: 2, repeat: Infinity, delay: 1 }}
                            className="absolute -right-2 top-1/2 transform -translate-y-1/2 text-white/60"
                          >
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                              <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                            </svg>
                          </motion.div>
                        )}
                      </motion.div>
                    ))}

                    {/* Add some padding at the end for better scroll experience */}
                    <div className="flex-shrink-0 w-4" />
                  </div>
                </div>
              </motion.div>
            )}

            {/* Suppliers Section */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="space-y-6"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <TrendingUp className="text-blue-400" size={28} />
                  <h2 className="text-2xl font-bold text-white">
                    {filteredSuppliers.length} Suppliers Found
                  </h2>
                </div>
                <div className="text-white/60 text-sm">
                  Sorted by {sortBy === 'popular' ? 'popularity' : sortBy}
                </div>
              </div>

              {/* Suppliers Grid/List */}
              <div className={viewMode === 'grid'
                ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                : "space-y-6"
              }>
                <AnimatePresence>
                  {filteredSuppliers.map((supplier, index) => (
                    <motion.div
                      key={supplier.id}
                      initial={{ opacity: 0, y: 30, scale: 0.9 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: -30, scale: 0.9 }}
                      transition={{
                        duration: 0.6,
                        delay: index * 0.1,
                        type: "spring",
                        stiffness: 100
                      }}
                      whileHover={{
                        scale: 1.02,
                        y: -8,
                        transition: { duration: 0.3 }
                      }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleSupplierClick(supplier.id)}
                      className="group cursor-pointer"
                    >
                      <div className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 overflow-hidden shadow-2xl hover:shadow-3xl transition-all duration-500">
                        {/* Supplier Image */}
                        <div className="relative h-48 overflow-hidden">
                          <motion.img
                            src={supplier.logoUrl}
                            alt={supplier.name}
                            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                            whileHover={{ scale: 1.1 }}
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />

                          {/* Status Badge */}
                          <div className="absolute top-4 left-4">
                            <motion.span
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ delay: 0.3 }}
                              className={`px-3 py-1 text-xs font-medium rounded-full backdrop-blur-sm ${
                                isSupplierOpen(supplier.openHours)
                                  ? 'bg-green-500/80 text-white'
                                  : 'bg-red-500/80 text-white'
                              }`}
                            >
                              {isSupplierOpen(supplier.openHours) ? 'Open Now' : 'Closed'}
                            </motion.span>
                          </div>

                          {/* Favorite Button */}
                          <motion.button
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleFavorite(supplier.id);
                            }}
                            className="absolute top-4 right-4 p-2 bg-white/20 backdrop-blur-sm rounded-full hover:bg-white/30 transition-all duration-200"
                          >
                            <Heart
                              size={18}
                              className={`${
                                favorites.has(supplier.id)
                                  ? 'text-red-400 fill-current'
                                  : 'text-white'
                              }`}
                            />
                          </motion.button>
                        </div>

                        {/* Supplier Info */}
                        <div className="p-6 space-y-4">
                          <div>
                            <h3 className="text-xl font-bold text-white mb-2 group-hover:text-blue-300 transition-colors">
                              {supplier.name}
                            </h3>

                            {/* Rating and Stats */}
                            <div className="flex items-center gap-4 text-sm text-white/70">
                              <div className="flex items-center gap-1">
                                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                                <span className="font-medium">{supplier.rating}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Clock className="w-4 h-4" />
                                <span>{supplier.deliveryTime}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Phone className="w-4 h-4" />
                                <span>{supplier.phone}</span>
                              </div>
                            </div>
                          </div>

                          {/* Tags */}
                          <div className="flex flex-wrap gap-2">
                            {supplier.tags.slice(0, 3).map((tag) => (
                              <span
                                key={tag}
                                className="px-3 py-1 bg-white/10 text-white/80 text-xs rounded-full border border-white/20"
                              >
                                {tag}
                              </span>
                            ))}
                            {supplier.tags.length > 3 && (
                              <span className="px-3 py-1 bg-white/10 text-white/60 text-xs rounded-full border border-white/20">
                                +{supplier.tags.length - 3} more
                              </span>
                            )}
                          </div>

                          {/* Hours */}
                          <div className="flex items-center justify-between pt-2 border-t border-white/10">
                            <span className="text-white/60 text-sm">{supplier.openHours}</span>
                            <motion.button
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={(e) => {
                                e.stopPropagation();
                                // Share functionality
                              }}
                              className="p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-200"
                            >
                              <Share2 size={16} className="text-white/60" />
                            </motion.button>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>

              {/* Empty State */}
              {filteredSuppliers.length === 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                  className="text-center py-16"
                >
                  <motion.div
                    animate={{
                      scale: [1, 1.1, 1],
                      rotate: [0, 5, -5, 0]
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    className="text-8xl mb-6"
                  >
                    🏪
                  </motion.div>
                  <h3 className="text-2xl font-bold text-white mb-4">No suppliers found</h3>
                  <p className="text-white/60 text-lg mb-8 max-w-md mx-auto">
                    We couldn't find any suppliers matching your criteria. Try adjusting your search or filters.
                  </p>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={clearSearch}
                    className="px-8 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-medium hover:shadow-lg transition-all duration-200"
                  >
                    Clear Search
                  </motion.button>
                </motion.div>
              )}
            </motion.div>
              </>
            )}
          </div>
        </div>
    </>
  );
};

export default SuppliersPage;